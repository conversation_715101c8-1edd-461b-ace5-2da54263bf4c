export 'dart:developer';

export 'package:cloud_firestore/cloud_firestore.dart';
export 'package:get/get.dart';
// Remove the flutter_web_plugins export for now
export 'package:get_storage/get_storage.dart';
export 'package:logestics/firebase_service/firebase_auth_service.dart';
export 'package:logestics/firebase_service/firebase_services.dart';
export 'package:logestics/core/utils/mixins/auto_refresh_mixin.dart';
export 'package:logestics/features/authentication/domain/repositories/auth_repository.dart';
export 'package:logestics/features/authentication/domain/repositories/auth_repository_imp.dart';
export 'package:logestics/features/authentication/domain/useCases/forget_password_use_case.dart';
export 'package:logestics/features/authentication/domain/useCases/login_use_case.dart';
export 'package:logestics/features/authentication/domain/useCases/sign_out_use_case.dart';
export 'package:logestics/features/authentication/domain/useCases/signup_use_case.dart';
export 'package:logestics/features/authentication/presentation/controllers/forget_password_component_controller.dart';
export 'package:logestics/features/authentication/presentation/controllers/signup_controller.dart';
export 'package:logestics/features/company/domain/use_cases/listen_to_invoice_use_case.dart';
export 'package:logestics/features/company/domain/use_cases/listen_to_voucher_use_case.dart';
export 'package:logestics/features/company/presentation/conrollers/company_controller.dart';
export 'package:logestics/features/finance/accounts/presentation/controllers/account_controller.dart';
export 'package:logestics/features/finance/accounts/presentation/controllers/account_transaction_controller.dart';
export 'package:logestics/features/finance/accounts/repositories/account_repository.dart';
export 'package:logestics/features/finance/accounts/repositories/account_transaction_repository.dart';
export 'package:logestics/features/finance/deposit_categories/presentation/controllers/deposit_category_controller.dart';
export 'package:logestics/features/finance/deposit_categories/repositories/deposit_category_repository.dart';
export 'package:logestics/features/finance/deposits/presentation/controllers/deposit_controller.dart';
export 'package:logestics/features/finance/deposits/repository/deposit_repository.dart';
export 'package:logestics/features/finance/expense_categories/presentation/controllers/expense_category_controller.dart';
export 'package:logestics/features/finance/expense_categories/repositories/expense_category_repository.dart';
export 'package:logestics/features/finance/expenses/presentation/controllers/expense_category_controller.dart';
export 'package:logestics/features/finance/expenses/presentation/controllers/expense_controller.dart';
export 'package:logestics/features/finance/expenses/repositories/expense_repository.dart';
export 'package:logestics/features/finance/fuel_cards/presentation/controllers/fuel_card_controller.dart';
export 'package:logestics/features/finance/fuel_cards/presentation/controllers/fuel_expense_controller.dart';
export 'package:logestics/features/finance/fuel_cards/repositories/fuel_card_repository.dart';
export 'package:logestics/features/finance/fuel_cards/usecases/add_fuel_rate_use_case.dart';
export 'package:logestics/features/finance/fuel_cards/usecases/create_fuel_card_use_case.dart';
export 'package:logestics/features/finance/fuel_cards/usecases/get_fuel_cards_use_case.dart';
export 'package:logestics/features/finance/fuel_cards/usecases/get_fuel_rate_history_use_case.dart';
export 'package:logestics/features/finance/fuel_cards/usecases/get_latest_fuel_rate_use_case.dart';
export 'package:logestics/features/finance/loans/presentation/controllers/loans_controller.dart';
export 'package:logestics/features/finance/loans/presentation/controllers/loan_history_controller.dart';
export 'package:logestics/features/finance/loans/presentation/controllers/loan_requests_controller.dart';
export 'package:logestics/features/finance/loans/repositories/company_repository.dart';
export 'package:logestics/features/finance/loans/repositories/loan_repository.dart';
export 'package:logestics/features/finance/loans/usecases/get_companies_use_case.dart';
export 'package:logestics/features/finance/loans/usecases/get_loan_requests_use_case.dart';
export 'package:logestics/features/finance/loans/usecases/get_loans_use_case.dart';
export 'package:logestics/features/finance/loans/usecases/loan_action_use_cases.dart';
export 'package:logestics/features/finance/loans/usecases/request_loan_use_case.dart';
export 'package:logestics/features/finance/payees/presentation/payee_controller.dart';
export 'package:logestics/features/finance/payees/repositories/payee_repository.dart';
export 'package:logestics/features/finance/brokers/repositories/broker_repository.dart';
export 'package:logestics/features/finance/brokers/presentation/controllers/broker_controller.dart';
export 'package:logestics/features/finance/check_usage/repositories/check_usage_repository.dart';
export 'package:logestics/features/finance/check_usage/presentation/controllers/check_usage_controller.dart';
export 'package:logestics/features/finance/payers/presentation/payer_controller.dart';
export 'package:logestics/features/finance/payers/repository/payer_repository.dart';
export 'package:logestics/features/user/presentation/controllers/user_controller.dart';
export 'package:logestics/features/invoices/repositories/invoice_repository.dart';
export 'package:logestics/features/invoices/presentation/controllers/invoice_form_controller.dart';
export 'package:logestics/features/invoices/use_cases/create_invoice_use_case.dart';
export 'package:logestics/features/invoices/use_cases/get_highest_invoice_number_use_case.dart';
export 'package:logestics/features/invoices/use_cases/update_invoice_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/district_use_case/create_district_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/district_use_case/delete_district_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/district_use_case/get_district_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/region_use_case/create_region_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/region_use_case/delete_region_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/region_use_case/get_region_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/station_use_case/create_station_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/station_use_case/delete_station_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/station_use_case/get_station_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/station_use_case/get_stations_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/station_use_case/update_station_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/zone_use_case/create_zone_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/zone_use_case/delete_zone_use_case.dart';
export 'package:logestics/features/locations/domain/usecases/zone_use_case/get_zones_use_case.dart';
export 'package:logestics/features/locations/presentation/districts/controller/add_district_controller.dart';
export 'package:logestics/features/locations/presentation/districts/controller/district_list_controller.dart';
export 'package:logestics/features/locations/presentation/regions/controller/add_region_controller.dart';
export 'package:logestics/features/locations/presentation/regions/controller/region_list_controller.dart';
export 'package:logestics/features/locations/presentation/stations/controller/add_station_controller.dart';
export 'package:logestics/features/locations/presentation/stations/controller/station_list_controller.dart';
export 'package:logestics/features/locations/presentation/zones/controller/add_zone_controller.dart';
export 'package:logestics/features/locations/presentation/zones/controller/zone_list_controller.dart';
export 'package:logestics/features/locations/repositories/district_repository.dart';
export 'package:logestics/features/locations/repositories/region_repository.dart';
export 'package:logestics/features/locations/repositories/station_repository.dart';
export 'package:logestics/features/locations/repositories/zone_repository.dart';
export 'package:logestics/features/voucher/presentation/controllers/add_voucher_controller.dart';
export 'package:logestics/features/voucher/presentation/controllers/voucher_list_controller.dart';
export 'package:logestics/features/voucher/presentation/controllers/voucher_migration_controller.dart';
export 'package:logestics/features/voucher/repositories/voucher_repository.dart';
export 'package:logestics/features/voucher/use_cases/create_voucher_use_case.dart';
export 'package:logestics/features/voucher/use_cases/update_voucher_use_case.dart';
export 'package:logestics/features/voucher/use_cases/delete_voucher_use_case.dart';
export 'package:logestics/firebase_service/finance/account_firebase_service.dart';
export 'package:logestics/firebase_service/finance/account_transaction_firebase_service.dart';
export 'package:logestics/firebase_service/finance/company_firebase_service.dart';
export 'package:logestics/firebase_service/finance/deposit_category_firebase_service.dart';
export 'package:logestics/firebase_service/finance/deposit_firebase_service.dart';
export 'package:logestics/firebase_service/finance/expense_category_firebase_service.dart';
export 'package:logestics/firebase_service/finance/expense_firebase_service.dart';
export 'package:logestics/firebase_service/finance/fuel_card_firebase_service.dart';
export 'package:logestics/firebase_service/finance/loan_firebase_service.dart';
export 'package:logestics/firebase_service/finance/payee_firebase_service.dart';
export 'package:logestics/firebase_service/finance/payer_firebase_service.dart';
export 'package:logestics/firebase_service/finance/broker_firebase_service.dart';
export 'package:logestics/firebase_service/finance/check_usage_firebase_service.dart';
export 'package:logestics/firebase_service/invoices/invoice_crud_firebase_service.dart';
export 'package:logestics/firebase_service/locations/district_firebase_service.dart';
export 'package:logestics/firebase_service/locations/region_firebase_service.dart';
export 'package:logestics/firebase_service/locations/station_firebase_service.dart';
export 'package:logestics/firebase_service/locations/zone_firebase_service.dart';
export 'package:logestics/firebase_service/voucher/voucher_crud_firebase_service.dart';
export 'package:logestics/firebase_service/backup_restore_firebase_service.dart';
export 'package:logestics/features/backup_restore/presentation/controllers/backup_restore_controller.dart';

// Bills exports
export '../firebase_service/finance/bill_firebase_service.dart';
export '../features/finance/bills/repositories/bill_repository.dart';
export '../features/finance/bills/use_cases/get_bills_use_case.dart';
export '../features/finance/bills/use_cases/delete_bill_use_case.dart';
export '../features/finance/bills/use_cases/update_bill_status_use_case.dart';
export '../features/finance/bills/presentation/controllers/bills_list_controller.dart';

// Chart of Accounts exports
export '../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
export '../features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';
export '../features/accounting/chart_of_accounts/presentation/controllers/chart_of_accounts_controller.dart';
export '../models/finance/chart_of_accounts_model.dart';

// Journal Entry exports
export '../firebase_service/accounting/journal_entry_firebase_service.dart';
export '../firebase_service/accounting/general_ledger_firebase_service.dart';
export '../features/accounting/journal_entries/repositories/journal_entry_repository.dart';
export '../features/accounting/journal_entries/presentation/controllers/journal_entry_controller.dart';
export '../features/accounting/general_ledger/presentation/controllers/general_ledger_controller.dart';
export '../features/accounting/chart_of_accounts/presentation/controllers/account_ledger_controller.dart';
export '../core/services/automatic_journal_entry_service.dart';
export '../core/services/financial_mock_data_service.dart';
export '../core/services/account_ledger_service.dart';
export '../core/services/voucher_journal_integration_service.dart';
export '../core/services/voucher_accounting_hook_service.dart';
export '../core/services/voucher_loan_integration_service.dart';
export '../core/services/transaction_account_mapping_service.dart';
export '../core/services/voucher_payment_settings_service.dart';
export '../core/services/voucher_chart_of_accounts_migration_service.dart';
export '../core/services/balance_recalculation_service.dart';
export '../features/accounting/chart_of_accounts/services/mock_data_service.dart';
export '../features/accounting/chart_of_accounts/services/account_journal_transaction_service.dart';

export '../features/accounting/chart_of_accounts/presentation/controllers/account_journal_transaction_controller.dart';
export '../features/accounting/mock_data/presentation/controllers/financial_mock_data_controller.dart';

// Financial Reporting exports
export '../firebase_service/accounting/trial_balance_firebase_service.dart';
export '../features/accounting/trial_balance/repositories/trial_balance_repository.dart';
export '../features/accounting/trial_balance/presentation/controllers/trial_balance_controller.dart';

export '../firebase_service/accounting/profit_loss_firebase_service.dart';
export '../features/accounting/profit_loss/repositories/profit_loss_repository.dart';
export '../features/accounting/profit_loss/presentation/controllers/profit_loss_controller.dart';

export '../firebase_service/accounting/balance_sheet_firebase_service.dart';
export '../features/accounting/balance_sheet/repositories/balance_sheet_repository.dart';
export '../features/accounting/balance_sheet/presentation/controllers/balance_sheet_controller.dart';

export '../features/accounting/dashboard/firebase_services/financial_dashboard_firebase_service.dart';
export '../features/accounting/dashboard/repositories/financial_dashboard_repository.dart';
export '../features/accounting/dashboard/presentation/controllers/financial_dashboard_controller.dart';

// Aged Reports exports
export '../firebase_service/accounting/aged_reports_firebase_service.dart';
export '../features/accounting/aged_reports/repositories/aged_reports_repository.dart';
export '../features/accounting/aged_reports/presentation/controllers/aged_reports_controller.dart';

// Year-End Closing exports
export '../firebase_service/accounting/year_end_closing_firebase_service.dart';
export '../firebase_service/accounting/fiscal_period_firebase_service.dart';
export '../features/accounting/fiscal_periods/repositories/fiscal_period_repository.dart';
export '../features/accounting/fiscal_periods/repositories/year_end_closing_repository.dart';
export '../features/accounting/fiscal_periods/presentation/controllers/fiscal_period_controller.dart';

// Cash Flow Statement exports
export '../firebase_service/accounting/cash_flow_statement_firebase_service.dart';
export '../features/accounting/cash_flow_statement/repositories/cash_flow_statement_repository.dart';
export '../features/accounting/cash_flow_statement/presentation/controllers/cash_flow_statement_controller.dart';

export '../features/authentication/presentation/controllers/login_component_controller.dart';
export '../features/dashboard/presentation/controllers/dashboard_screen_controller.dart';
export '../features/dashboard/presentation/controllers/dashboard_data_controller.dart';

// PDF Generation exports
export '../services/pdf_generation_service.dart';
export '../features/finance/accounts/presentation/controllers/pdf_generation_controller.dart';

// Slab Rate Calculation Service
export '../services/slab_rate_calculation_service.dart';

// Slab Management exports
export '../firebase_service/slab/slab_firebase_service.dart';
export '../features/slab/repositories/slab_repository.dart';
export '../features/slab/repositories/slab_repository_impl.dart';
export '../features/slab/domain/usecases/create_slab_use_case.dart';
export '../features/slab/domain/usecases/get_slabs_use_case.dart';
export '../features/slab/domain/usecases/update_slab_use_case.dart';
export '../features/slab/domain/usecases/delete_slab_use_case.dart';
export '../features/slab/domain/usecases/get_active_slabs_for_district_use_case.dart';
export '../features/slab/presentation/controllers/slab_list_controller.dart';
export '../features/slab/presentation/controllers/slab_form_controller.dart';

// Asset Management exports
export '../firebase_service/asset/asset_firebase_service.dart';
export '../firebase_service/asset/asset_maintenance_firebase_service.dart';
export '../firebase_service/asset/asset_audit_firebase_service.dart';
export '../repositories/asset/asset_repository.dart';
export '../repositories/asset/asset_repository_impl.dart';
export '../repositories/asset/asset_audit_repository.dart';
export '../features/asset_management/presentation/controllers/asset_list_controller.dart';
export '../features/asset_management/presentation/controllers/asset_form_controller.dart';
export '../features/asset_management/presentation/controllers/asset_detail_controller.dart';
export '../features/asset_management/presentation/controllers/maintenance_history_controller.dart';
export '../features/asset_management/presentation/controllers/maintenance_form_controller.dart';
export '../features/asset_management/presentation/controllers/audit_trail_controller.dart';
export '../features/asset_management/presentation/controllers/audit_trail_export_controller.dart';
export '../features/asset_management/presentation/controllers/asset_export_controller.dart';
