import 'barrel.dart';

class AppBindings extends Bindings {
  @override
  void dependencies() {
    _registerCoreServices();
    _registerAuthDependencies();
    _registerLocationDependencies();
    _registerFinancialDependencies();
    _registerFuelDependencies();
    _registerCompanyDependencies();
    _registerSlabDependencies();
    _registerAssetManagementDependencies();
    _registerAccountingDependencies();
  }

  /// Register core Firebase services
  void _registerCoreServices() {
    Get.put(FirebaseServices(), permanent: true);
    Get.put(FirebaseAuthService(), permanent: true);
    // Get.put(FirebaseAdminService(), permanent: true);

    // User Controller for managing current user info
    Get.put(UserController(), permanent: true);

    // Backup/Restore Service
    Get.put(BackupRestoreFirebaseService(), permanent: true);

    // Backup/Restore Controller
    Get.put(BackupRestoreController(), permanent: true);
  }

  /// Register authentication related dependencies
  void _registerAuthDependencies() {
    // Repository
    Get.lazyPut<AuthRepository>(
      () => AuthRepositoryImpl(Get.find<FirebaseAuthService>()),
      fenix: true,
    );

    // Use Cases
    Get.lazyPut(() => SignupUseCase(authRepository: Get.find<AuthRepository>()),
        fenix: true);
    Get.lazyPut(() => LoginUseCase(authRepository: Get.find<AuthRepository>()),
        fenix: true);
    Get.lazyPut(() => SignOutUseCase(Get.find<AuthRepository>()), fenix: true);
    Get.lazyPut(
        () => ForgetPasswordUseCase(authRepository: Get.find<AuthRepository>()),
        fenix: true);

    // Controllers
    Get.lazyPut(
        () => SignupController(signupUseCase: Get.find<SignupUseCase>()),
        fenix: true);
    Get.lazyPut(
        () => LoginComponentController(loginUseCase: Get.find<LoginUseCase>()),
        fenix: true);
    Get.lazyPut(
        () => DashboardScreenController(
            signOutUseCase: Get.find<SignOutUseCase>()),
        fenix: true);

    // Dashboard Data Controller for real-time insights
    Get.lazyPut(() => DashboardDataController(), fenix: true);
    Get.lazyPut(
        () => ForgetPasswordComponentController(
            forgetPasswordUseCase: Get.find<ForgetPasswordUseCase>()),
        fenix: true);
  }

  /// Register location management dependencies (Zone, Region, District, Station)
  void _registerLocationDependencies() {
    // Firebase Services
    Get.lazyPut(() => ZoneFirebaseService());
    Get.lazyPut(() => RegionFirebaseService());
    Get.lazyPut(() => DistrictFirebaseService());
    Get.lazyPut(() => StationFirebaseService());

    // Repositories
    Get.lazyPut<ZoneRepository>(
        () => ZoneRepositoryImpl(Get.find<ZoneFirebaseService>()));
    Get.lazyPut<RegionRepository>(
        () => RegionRepositoryImpl(Get.find<RegionFirebaseService>()));
    Get.lazyPut<DistrictRepository>(
        () => DistrictRepositoryImpl(Get.find<DistrictFirebaseService>()));
    Get.lazyPut<StationRepository>(
        () => StationRepositoryImpl(Get.find<StationFirebaseService>()));

    // Zone Use Cases
    Get.lazyPut(() => CreateZoneUseCase(Get.find<ZoneRepository>()));
    Get.lazyPut(() => GetZonesUseCase(Get.find<ZoneRepository>()));
    Get.lazyPut(() => DeleteZoneUseCase(Get.find<ZoneRepository>()));

    // Region Use Cases
    Get.lazyPut(() => CreateRegionUseCase(Get.find<RegionRepository>()));
    Get.lazyPut(() => GetRegionsUseCase(Get.find<RegionRepository>()));
    Get.lazyPut(() => DeleteRegionUseCase(Get.find<RegionRepository>()));

    // District Use Cases
    Get.lazyPut(() => CreateDistrictUseCase(Get.find<DistrictRepository>()));
    Get.lazyPut(() => GetDistrictsUseCase(Get.find<DistrictRepository>()));
    Get.lazyPut(() => DeleteDistrictUseCase(Get.find<DistrictRepository>()));

    // Station Use Cases
    Get.lazyPut(() => CreateStationUseCase(Get.find<StationRepository>()));
    Get.lazyPut(() => GetStationsUseCase(Get.find<StationRepository>()));
    Get.lazyPut(
        () => GetStationByDistrictIdUseCase(Get.find<StationRepository>()));
    Get.lazyPut(() => UpdateStationUseCase(Get.find<StationRepository>()));
    Get.lazyPut(() => DeleteStationUseCase(Get.find<StationRepository>()));

    // Controllers
    Get.lazyPut(() =>
        AddZoneController(createZoneUseCase: Get.find<CreateZoneUseCase>()));
    Get.lazyPut(() => ZoneListController(
          getZonesUseCase: Get.find<GetZonesUseCase>(),
          deleteZoneUseCase: Get.find<DeleteZoneUseCase>(),
        ));

    Get.lazyPut(() => AddRegionController(
        createRegionUseCase: Get.find<CreateRegionUseCase>()));
    Get.lazyPut(() => RegionListController(
          getRegionsUseCase: Get.find<GetRegionsUseCase>(),
          deleteRegionUseCase: Get.find<DeleteRegionUseCase>(),
        ));

    Get.lazyPut(() => AddDistrictController(
        createDistrictUseCase: Get.find<CreateDistrictUseCase>()));
    Get.lazyPut(() => DistrictListController(
          getDistrictsUseCase: Get.find<GetDistrictsUseCase>(),
          deleteDistrictUseCase: Get.find<DeleteDistrictUseCase>(),
        ));

    Get.lazyPut(() => AddStationController(
          createStationUseCase: Get.find<CreateStationUseCase>(),
          updateStationUseCase: Get.find<UpdateStationUseCase>(),
          getDistrictsUseCase: Get.find<GetDistrictsUseCase>(),
        ));
    Get.lazyPut(() => StationListController(
          getStationsUseCase: Get.find<GetStationsUseCase>(),
          deleteStationUseCase: Get.find<DeleteStationUseCase>(),
        ));
  }

  /// Register financial dependencies (Invoice, Voucher, Loan, Deposit, Expense)
  void _registerFinancialDependencies() {
    // Firebase Services
    Get.lazyPut(() => InvoiceCrudFirebaseService(), fenix: true);
    Get.lazyPut(() => VoucherCrudFirebaseService(), fenix: true);
    Get.lazyPut(() => LoanFirebaseService(), fenix: true);
    Get.lazyPut(() => DepositFirebaseService());
    Get.lazyPut(() => DepositCategoryFirebaseService());
    Get.lazyPut(() => ExpenseFirebaseService());
    Get.lazyPut(() => ExpenseCategoryFirebaseService());
    Get.lazyPut(() => AccountFirebaseService());
    Get.lazyPut(() => PayerFirebaseService());
    Get.lazyPut(() => PayeeFirebaseService());
    Get.lazyPut(() => BrokerFirebaseService());
    Get.lazyPut(() => CheckUsageFirebaseService());
    Get.lazyPut(() => AccountTransactionFirebaseService());
    Get.lazyPut(() => BillFirebaseService());

    // Repositories
    Get.lazyPut<InvoiceRepository>(
        () => InvoiceRepositoryImpl(Get.find<InvoiceCrudFirebaseService>()),
        fenix: true);
    Get.lazyPut<VoucherRepository>(
        () => VoucherRepositoryImp(Get.find<VoucherCrudFirebaseService>()),
        fenix: true);
    Get.lazyPut<LoanRepository>(
        () => LoanRepositoryImpl(Get.find<LoanFirebaseService>()),
        fenix: true);
    Get.lazyPut<DepositRepository>(
        () => DepositRepositoryImpl(Get.find<DepositFirebaseService>()));
    Get.lazyPut<DepositCategoryRepository>(() => DepositCategoryRepositoryImpl(
        Get.find<DepositCategoryFirebaseService>()));
    Get.lazyPut<ExpenseRepository>(() => ExpenseRepositoryImpl(
        Get.find<ExpenseFirebaseService>(),
        Get.find<AccountFirebaseService>()));
    Get.lazyPut<ExpenseCategoryRepository>(() => ExpenseCategoryRepositoryImpl(
        Get.find<ExpenseCategoryFirebaseService>()));
    Get.lazyPut<AccountRepository>(
        () => AccountRepositoryImpl(Get.find<AccountFirebaseService>()));
    Get.lazyPut<PayerRepository>(
        () => PayerRepositoryImpl(Get.find<PayerFirebaseService>()));
    Get.lazyPut<PayeeRepository>(
        () => PayeeRepositoryImpl(Get.find<PayeeFirebaseService>()));
    Get.lazyPut<BrokerRepository>(
        () => BrokerRepository(Get.find<BrokerFirebaseService>()));
    Get.lazyPut<CheckUsageRepository>(
        () => CheckUsageRepository(Get.find<CheckUsageFirebaseService>()));
    Get.lazyPut<AccountTransactionRepository>(() =>
        AccountTransactionRepositoryImpl(
            Get.find<AccountTransactionFirebaseService>()));
    Get.lazyPut<BillRepository>(
        () => BillRepositoryImpl(Get.find<BillFirebaseService>()),
        fenix: true);

    // Invoice Use Cases
    Get.lazyPut(() => CreateInvoiceUseCase(Get.find<InvoiceRepository>()),
        fenix: true);
    Get.lazyPut(() => UpdateInvoiceUseCase(Get.find<InvoiceRepository>()),
        fenix: true);
    Get.lazyPut(
        () => GetHighestInvoiceNumberUseCase(Get.find<InvoiceRepository>()),
        fenix: true);
    Get.lazyPut(() => ListenToInvoicesUseCase(Get.find<InvoiceRepository>()));

    // Invoice Controller
    Get.lazyPut<InvoiceFormController>(
        () => InvoiceFormController(
              firebaseServices: Get.find<FirebaseServices>(),
              createInvoiceUseCase: Get.find<CreateInvoiceUseCase>(),
              updateInvoiceUseCase: Get.find<UpdateInvoiceUseCase>(),
              getDistrictsUseCase: Get.find<GetDistrictsUseCase>(),
              getStationUseCase: Get.find<GetStationByDistrictIdUseCase>(),
              getHighestInvoiceNumberUseCase:
                  Get.find<GetHighestInvoiceNumberUseCase>(),
              currentInvoice: null, // Will be set dynamically when needed
            ),
        fenix: true);

    // Voucher Use Cases
    Get.lazyPut(() => CreateVoucherUseCase(Get.find<VoucherRepository>()),
        fenix: true);
    Get.lazyPut(() => UpdateVoucherUseCase(Get.find<VoucherRepository>()),
        fenix: true);
    Get.lazyPut(() => DeleteVoucherUseCase(Get.find<VoucherRepository>()),
        fenix: true);
    Get.lazyPut(() => ListenToVouchersUseCase(Get.find<VoucherRepository>()));

    // Loan Use Cases
    Get.lazyPut(() => RequestLoanUseCase(Get.find<LoanRepository>()),
        fenix: true);
    Get.lazyPut(
        () => GetIncomingLoanRequestsUseCase(Get.find<LoanRepository>()),
        fenix: true);
    Get.lazyPut(
        () => GetOutgoingLoanRequestsUseCase(Get.find<LoanRepository>()),
        fenix: true);
    Get.lazyPut(() => ApproveLoanUseCase(Get.find<LoanRepository>()),
        fenix: true);
    Get.lazyPut(() => RejectLoanUseCase(Get.find<LoanRepository>()),
        fenix: true);
    Get.lazyPut(() => RepayLoanUseCase(Get.find<LoanRepository>()),
        fenix: true);
    Get.lazyPut(() => GetActiveLoansUseCase(Get.find<LoanRepository>()),
        fenix: true);
    Get.lazyPut(() => GetLoanHistoryUseCase(Get.find<LoanRepository>()),
        fenix: true);

    // Updated: Use GetUsersUseCase instead of GetCompaniesUseCase
    Get.lazyPut(() => GetUsersUseCase(Get.find<CompanyRepository>()),
        fenix: true);

    // Bills Use Cases
    Get.lazyPut(() => GetBillsUseCase(Get.find<BillRepository>()), fenix: true);
    Get.lazyPut(() => DeleteBillUseCase(Get.find<BillRepository>()),
        fenix: true);
    Get.lazyPut(() => UpdateBillStatusUseCase(Get.find<BillRepository>()),
        fenix: true);

    // Controllers
    Get.lazyPut<AddVoucherController>(
        () => AddVoucherController(
              currentVoucher: Get.arguments?['voucher'],
              createVoucherUseCase: Get.find<CreateVoucherUseCase>(),
              updateVoucherUseCase: Get.find<UpdateVoucherUseCase>(),
            ),
        fenix: true);

    Get.lazyPut<VoucherListController>(
        () => VoucherListController(
              deleteVoucherUseCase: Get.find<DeleteVoucherUseCase>(),
            ),
        fenix: true);

    Get.lazyPut(
        () => LoansController(
              requestLoanUseCase: Get.find<RequestLoanUseCase>(),
              getIncomingLoanRequestsUseCase:
                  Get.find<GetIncomingLoanRequestsUseCase>(),
              getOutgoingLoanRequestsUseCase:
                  Get.find<GetOutgoingLoanRequestsUseCase>(),
              approveLoanUseCase: Get.find<ApproveLoanUseCase>(),
              rejectLoanUseCase: Get.find<RejectLoanUseCase>(),
              repayLoanUseCase: Get.find<RepayLoanUseCase>(),
              getActiveLoansUseCase: Get.find<GetActiveLoansUseCase>(),
              getLoanHistoryUseCase: Get.find<GetLoanHistoryUseCase>(),
              getUsersUseCase: Get.find<GetUsersUseCase>(),
              accountRepository: Get.find<AccountRepository>(),
              chartOfAccountsRepository: Get.find<ChartOfAccountsRepository>(),
              loanService: Get.find<LoanFirebaseService>(),
            ),
        fenix: true);

    Get.lazyPut(
        () => LoanHistoryController(
              getLoanHistoryUseCase: Get.find<GetLoanHistoryUseCase>(),
            ),
        fenix: true);

    Get.lazyPut(
        () => LoanRequestsController(
              getIncomingLoanRequestsUseCase:
                  Get.find<GetIncomingLoanRequestsUseCase>(),
              getOutgoingLoanRequestsUseCase:
                  Get.find<GetOutgoingLoanRequestsUseCase>(),
              approveLoanUseCase: Get.find<ApproveLoanUseCase>(),
              rejectLoanUseCase: Get.find<RejectLoanUseCase>(),
              accountRepository: Get.find<AccountRepository>(),
              chartOfAccountsRepository: Get.find<ChartOfAccountsRepository>(),
            ),
        fenix: true);

    Get.lazyPut(() => DepositCategoryController(
        repository: Get.find<DepositCategoryRepository>()));
    Get.lazyPut(
        () => AccountController(repository: Get.find<AccountRepository>()));
    Get.lazyPut(() => PayeeController(repository: Get.find<PayeeRepository>()));
    Get.lazyPut(() => PayerController(repository: Get.find<PayerRepository>()));
    Get.lazyPut(
        () => BrokerController(repository: Get.find<BrokerRepository>()));
    Get.lazyPut(() =>
        CheckUsageController(repository: Get.find<CheckUsageRepository>()));
    Get.lazyPut(() => ExpenseCategoryManagementController(
        Get.find<ExpenseCategoryRepository>()));

    Get.lazyPut(() => DepositController(
          depositRepository: Get.find<DepositRepository>(),
          accountRepository: Get.find<AccountRepository>(),
          payerRepository: Get.find<PayerRepository>(),
          categoryRepository: Get.find<DepositCategoryRepository>(),
        ));

    Get.lazyPut<ExpenseController>(() => ExpenseController(
          expenseRepository: Get.find<ExpenseRepository>(),
          accountRepository: Get.find<AccountRepository>(),
          payeeRepository: Get.find<PayeeRepository>(),
          categoryRepository: Get.find<ExpenseCategoryRepository>(),
        ));

    Get.lazyPut<ExpensesCategoryController>(() => ExpensesCategoryController(
          repository: Get.find<ExpenseCategoryRepository>(),
        ));

    Get.lazyPut<AccountTransactionController>(
        () => AccountTransactionController(
              repository: Get.find<AccountTransactionRepository>(),
              categoryRepository: Get.find<ExpenseCategoryRepository>(),
            ));

    Get.lazyPut<BillsListController>(
        () => BillsListController(
              deleteBillUseCase: Get.find<DeleteBillUseCase>(),
              updateBillStatusUseCase: Get.find<UpdateBillStatusUseCase>(),
              getBillsUseCase: Get.find<GetBillsUseCase>(),
            ),
        fenix: true);

    // PDF Generation Dependencies
    Get.lazyPut(() => PDFGenerationService(), fenix: true);
    Get.lazyPut<PDFGenerationController>(
        () => PDFGenerationController(
              accountRepository: Get.find<AccountRepository>(),
              transactionRepository: Get.find<AccountTransactionRepository>(),
              companyService: Get.find<CompanyFirebaseService>(),
              authService: Get.find<FirebaseAuthService>(),
              pdfService: Get.find<PDFGenerationService>(),
            ),
        fenix: true);
  }

  /// Register fuel management dependencies
  void _registerFuelDependencies() {
    // Firebase Service
    Get.lazyPut(() => FuelCardFirebaseService(), fenix: true);

    // Repository
    Get.lazyPut<FuelCardRepository>(
        () => FuelCardRepositoryImpl(Get.find<FuelCardFirebaseService>()),
        fenix: true);

    // Use Cases
    Get.lazyPut(() => CreateFuelCardUseCase(Get.find<FuelCardRepository>()),
        fenix: true);
    Get.lazyPut(() => GetFuelCardsUseCase(Get.find<FuelCardRepository>()),
        fenix: true);
    Get.lazyPut(() => AddFuelRateUseCase(Get.find<FuelCardRepository>()),
        fenix: true);
    Get.lazyPut(() => GetLatestFuelRateUseCase(Get.find<FuelCardRepository>()),
        fenix: true);
    Get.lazyPut(() => GetFuelRateHistoryUseCase(Get.find<FuelCardRepository>()),
        fenix: true);

    // Controllers
    Get.lazyPut(
        () => FuelCardController(
              repository: Get.find<FuelCardRepository>(),
              createFuelCardUseCase: Get.find<CreateFuelCardUseCase>(),
              getFuelCardsUseCase: Get.find<GetFuelCardsUseCase>(),
              addFuelRateUseCase: Get.find<AddFuelRateUseCase>(),
              getLatestFuelRateUseCase: Get.find<GetLatestFuelRateUseCase>(),
              getFuelRateHistoryUseCase: Get.find<GetFuelRateHistoryUseCase>(),
            ),
        fenix: true);

    Get.lazyPut(
        () => FuelExpenseController(
              fuelCardRepository: Get.find<FuelCardRepository>(),
            ),
        fenix: true);
  }

  /// Register company management dependencies
  void _registerCompanyDependencies() {
    // Firebase Service
    Get.lazyPut<CompanyFirebaseService>(() => CompanyFirebaseService(),
        fenix: true);

    // Repository
    Get.lazyPut<CompanyRepository>(
        () => CompanyRepositoryImpl(Get.find<CompanyFirebaseService>()),
        fenix: true);

    // Controller
    Get.lazyPut(() => CompanyController(
          Get.find<ListenToInvoicesUseCase>(),
          Get.find<ListenToVouchersUseCase>(),
        ));
  }

  /// Register slab management dependencies
  void _registerSlabDependencies() {
    // Firebase Service
    Get.lazyPut<SlabFirebaseService>(() => SlabFirebaseService(), fenix: true);

    // Repository
    Get.lazyPut<SlabRepository>(
        () => SlabRepositoryImpl(Get.find<SlabFirebaseService>()),
        fenix: true);

    // Use Cases
    Get.lazyPut(() => CreateSlabUseCase(Get.find<SlabRepository>()),
        fenix: true);
    Get.lazyPut(() => GetSlabsUseCase(Get.find<SlabRepository>()), fenix: true);
    Get.lazyPut(() => UpdateSlabUseCase(Get.find<SlabRepository>()),
        fenix: true);
    Get.lazyPut(() => DeleteSlabUseCase(Get.find<SlabRepository>()),
        fenix: true);
    Get.lazyPut(
        () => GetActiveSlabsForDistrictUseCase(Get.find<SlabRepository>()),
        fenix: true);

    // Slab Rate Calculation Service
    Get.lazyPut(
        () => SlabRateCalculationService(
              getActiveSlabsForDistrictUseCase:
                  Get.find<GetActiveSlabsForDistrictUseCase>(),
            ),
        fenix: true);

    // Controllers
    Get.lazyPut(
        () => SlabListController(
              getSlabsUseCase: Get.find<GetSlabsUseCase>(),
              deleteSlabUseCase: Get.find<DeleteSlabUseCase>(),
            ),
        fenix: true);

    Get.lazyPut(
        () => SlabFormController(
              createSlabUseCase: Get.find<CreateSlabUseCase>(),
              updateSlabUseCase: Get.find<UpdateSlabUseCase>(),
              getRegionsUseCase: Get.find<GetRegionsUseCase>(),
              getDistrictsUseCase: Get.find<GetDistrictsUseCase>(),
            ),
        fenix: true);
  }

  /// Register asset management dependencies
  void _registerAssetManagementDependencies() {
    // Firebase Services
    Get.lazyPut(() => AssetFirebaseService(), fenix: true);
    Get.lazyPut(() => AssetMaintenanceFirebaseService(), fenix: true);
    Get.lazyPut(() => AssetAuditFirebaseService(), fenix: true);

    // Repository
    Get.lazyPut<AssetRepository>(
        () => AssetRepositoryImpl(
              Get.find<AssetFirebaseService>(),
              Get.find<AssetMaintenanceFirebaseService>(),
            ),
        fenix: true);

    Get.lazyPut<AssetAuditRepository>(
        () => AssetAuditRepositoryImpl(Get.find<AssetAuditFirebaseService>()),
        fenix: true);

    // Controllers
    Get.lazyPut(() => AssetListController(Get.find<AssetRepository>()),
        fenix: true);

    Get.lazyPut(() => AssetFormController(Get.find<AssetRepository>()),
        fenix: true);

    Get.lazyPut(() => MaintenanceHistoryController(), fenix: true);

    Get.lazyPut(() => MaintenanceFormController(), fenix: true);

    Get.lazyPut(() => AssetExportController(), fenix: true);
  }

  /// Register accounting related dependencies
  void _registerAccountingDependencies() {
    // Firebase Services
    Get.lazyPut(() => ChartOfAccountsFirebaseService(), fenix: true);
    Get.lazyPut(() => JournalEntryFirebaseService(), fenix: true);
    Get.lazyPut(() => GeneralLedgerFirebaseService(), fenix: true);
    Get.lazyPut(() => TrialBalanceFirebaseService(), fenix: true);
    Get.lazyPut(() => ProfitLossFirebaseService(), fenix: true);
    Get.lazyPut(() => BalanceSheetFirebaseService(), fenix: true);
    Get.lazyPut(() => FinancialDashboardFirebaseService(), fenix: true);
    Get.lazyPut(() => AgedReportsFirebaseService(), fenix: true);
    Get.lazyPut(() => FiscalPeriodFirebaseService(), fenix: true);
    Get.lazyPut(() => YearEndClosingFirebaseService(), fenix: true);
    Get.lazyPut(() => CashFlowStatementFirebaseService(), fenix: true);

    // Balance Recalculation Service
    Get.lazyPut(() => BalanceRecalculationService(), fenix: true);

    // Mock Data Service
    Get.lazyPut(() => FinancialMockDataService(), fenix: true);

    // Repositories
    Get.lazyPut<ChartOfAccountsRepository>(
      () => ChartOfAccountsRepositoryImpl(
          Get.find<ChartOfAccountsFirebaseService>()),
      fenix: true,
    );
    Get.lazyPut<JournalEntryRepository>(
      () => JournalEntryRepositoryImpl(Get.find<JournalEntryFirebaseService>()),
      fenix: true,
    );
    Get.lazyPut<TrialBalanceRepository>(
      () => TrialBalanceRepositoryImpl(Get.find<TrialBalanceFirebaseService>()),
      fenix: true,
    );
    Get.lazyPut<FiscalPeriodRepository>(
      () => FiscalPeriodRepositoryImpl(Get.find<FiscalPeriodFirebaseService>()),
      fenix: true,
    );
    Get.lazyPut<YearEndClosingRepository>(
      () => YearEndClosingRepositoryImpl(
          Get.find<YearEndClosingFirebaseService>(),
          Get.find<FiscalPeriodFirebaseService>()),
      fenix: true,
    );
    Get.lazyPut<CashFlowStatementRepository>(
      () => CashFlowStatementRepositoryImpl(
          Get.find<CashFlowStatementFirebaseService>()),
      fenix: true,
    );

    // Services
    Get.lazyPut(
        () => AutomaticJournalEntryService(
              Get.find<ChartOfAccountsFirebaseService>(),
            ),
        fenix: true);

    Get.lazyPut(
        () => MockDataService(
              Get.find<ChartOfAccountsRepository>(),
              Get.find<JournalEntryRepository>(),
            ),
        fenix: true);

    Get.lazyPut(
        () => AccountJournalTransactionService(
              Get.find<JournalEntryRepository>(),
            ),
        fenix: true);

    // Voucher Integration Services
    Get.lazyPut(() => AccountLedgerService(), fenix: true);
    Get.lazyPut(
        () => TransactionAccountMappingService(
              Get.find<ChartOfAccountsFirebaseService>(),
            ),
        fenix: true);

    Get.lazyPut(
        () => VoucherJournalIntegrationService(
              Get.find<AutomaticJournalEntryService>(),
              Get.find<GeneralLedgerFirebaseService>(),
              Get.find<JournalEntryFirebaseService>(),
              Get.find<TransactionAccountMappingService>(),
              Get.find<AccountLedgerService>(),
            ),
        fenix: true);

    Get.lazyPut(() => VoucherAccountingHookService(), fenix: true);

    // Voucher Loan Integration Service
    Get.lazyPut(() => VoucherLoanIntegrationService(), fenix: true);

    // Voucher Payment Settings Service
    Get.lazyPut(() => VoucherPaymentSettingsService(), fenix: true);

    // Voucher Migration Services
    Get.lazyPut(() => VoucherChartOfAccountsMigrationService(), fenix: true);
    Get.lazyPut(() => VoucherMigrationController(), fenix: true);

    // Controllers
    Get.lazyPut(
        () => ChartOfAccountsController(
            repository: Get.find<ChartOfAccountsRepository>()),
        fenix: true);
    Get.lazyPut(
        () => JournalEntryController(
            repository: Get.find<JournalEntryRepository>(),
            chartOfAccountsRepository: Get.find<ChartOfAccountsRepository>()),
        fenix: true);
    Get.lazyPut(
        () => GeneralLedgerController(
            chartOfAccountsRepository: Get.find<ChartOfAccountsRepository>(),
            journalEntryRepository: Get.find<JournalEntryRepository>(),
            generalLedgerFirebaseService:
                Get.find<GeneralLedgerFirebaseService>()),
        fenix: true);
    Get.lazyPut(() => FinancialMockDataController(), fenix: true);

    Get.lazyPut(
        () => AccountJournalTransactionController(
            Get.find<AccountJournalTransactionService>()),
        fenix: true);

    Get.lazyPut(() => AccountLedgerController(), fenix: true);

    // Financial Reporting Controllers
    Get.lazyPut(
        () => TrialBalanceController(Get.find<TrialBalanceRepository>()),
        fenix: true);

    Get.lazyPut(() => ProfitLossController(), fenix: true);

    Get.lazyPut(() => BalanceSheetController(), fenix: true);

    Get.lazyPut(() => FinancialDashboardController(), fenix: true);

    // Aged Reports
    Get.lazyPut(() => AgedReportsRepository(), fenix: true);
    Get.lazyPut(() => AgedReportsController(), fenix: true);

    // Cash Flow Statement
    Get.lazyPut(() => CashFlowStatementController(), fenix: true);

    // Fiscal Periods
    Get.lazyPut(() => FiscalPeriodController(), fenix: true);
  }
}

class AppDependencyInjection {
  Future<void> init() async {
    try {
      await GetStorage.init();

      FirebaseFirestore.instance.settings = const Settings(
        persistenceEnabled: true,
      );
    } catch (e) {
      log('error $e');
    }
  }
}
